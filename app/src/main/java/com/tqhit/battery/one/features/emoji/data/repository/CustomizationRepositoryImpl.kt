package com.tqhit.battery.one.features.emoji.data.repository

import android.content.Context
import android.util.Log
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.floatPreferencesKey
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of CustomizationRepository using Jetpack DataStore.
 * Provides persistent storage for emoji battery customization settings.
 * 
 * Uses DataStore Preferences for type-safe, asynchronous data storage
 * that integrates well with the existing app architecture.
 */
@Singleton
class CustomizationRepositoryImpl @Inject constructor(
    @ApplicationContext private val context: Context
) : CustomizationRepository {
    
    companion object {
        private const val TAG = "CustomizationRepository"
        private const val DATASTORE_NAME = "emoji_battery_customization"
        
        // DataStore extension
        private val Context.customizationDataStore: DataStore<Preferences> by preferencesDataStore(
            name = DATASTORE_NAME
        )
        
        // Preference keys
        private val SELECTED_STYLE_ID = stringPreferencesKey("selected_style_id")
        private val SHOW_EMOJI = booleanPreferencesKey("show_emoji")
        private val SHOW_PERCENTAGE = booleanPreferencesKey("show_percentage")
        private val PERCENTAGE_FONT_SIZE = intPreferencesKey("percentage_font_size")
        private val EMOJI_SIZE_SCALE = floatPreferencesKey("emoji_size_scale")
        private val PERCENTAGE_COLOR = intPreferencesKey("percentage_color")
        private val IS_GLOBAL_ENABLED = booleanPreferencesKey("is_global_enabled")
        private val LAST_MODIFIED_TIMESTAMP = longPreferencesKey("last_modified_timestamp")
    }
    
    private val dataStore = context.customizationDataStore
    
    override fun getCustomizationConfigFlow(): Flow<CustomizationConfig> {
        return dataStore.data
            .catch { exception ->
                Log.e(TAG, "Error reading customization config", exception)
                emit(androidx.datastore.preferences.core.emptyPreferences())
            }
            .map { preferences ->
                mapPreferencesToConfig(preferences)
            }
    }
    
    override suspend fun getCustomizationConfig(): CustomizationConfig {
        return try {
            val preferences = dataStore.data.first()
            mapPreferencesToConfig(preferences)
        } catch (exception: Exception) {
            Log.e(TAG, "Error getting customization config", exception)
            CustomizationConfig.createDefault()
        }
    }
    
    override suspend fun saveCustomizationConfig(config: CustomizationConfig): Result<Unit> {
        return try {
            val validatedConfig = config.validated()
            dataStore.edit { preferences ->
                preferences[SELECTED_STYLE_ID] = validatedConfig.selectedStyleId
                preferences[SHOW_EMOJI] = validatedConfig.customConfig.showEmoji
                preferences[SHOW_PERCENTAGE] = validatedConfig.customConfig.showPercentage
                preferences[PERCENTAGE_FONT_SIZE] = validatedConfig.customConfig.percentageFontSizeDp
                preferences[EMOJI_SIZE_SCALE] = validatedConfig.customConfig.emojiSizeScale
                preferences[PERCENTAGE_COLOR] = validatedConfig.customConfig.percentageColor
                preferences[IS_GLOBAL_ENABLED] = validatedConfig.isGlobalEnabled
                preferences[LAST_MODIFIED_TIMESTAMP] = validatedConfig.lastModifiedTimestamp
            }
            Log.d(TAG, "Customization config saved successfully: ${validatedConfig.selectedStyleId}")
            Result.success(Unit)
        } catch (exception: Exception) {
            Log.e(TAG, "Error saving customization config", exception)
            Result.failure(exception)
        }
    }
    
    override suspend fun updateSelectedStyle(styleId: String): Result<Unit> {
        return try {
            val currentConfig = getCustomizationConfig()
            val updatedConfig = currentConfig.copy(
                selectedStyleId = styleId,
                lastModifiedTimestamp = System.currentTimeMillis()
            )
            saveCustomizationConfig(updatedConfig)
        } catch (exception: Exception) {
            Log.e(TAG, "Error updating selected style", exception)
            Result.failure(exception)
        }
    }
    
    override suspend fun updateGlobalEnabled(isEnabled: Boolean): Result<Unit> {
        return try {
            val currentConfig = getCustomizationConfig()
            val updatedConfig = currentConfig.copy(
                isGlobalEnabled = isEnabled,
                lastModifiedTimestamp = System.currentTimeMillis()
            )
            saveCustomizationConfig(updatedConfig)
        } catch (exception: Exception) {
            Log.e(TAG, "Error updating global enabled state", exception)
            Result.failure(exception)
        }
    }
    
    override suspend fun clearCustomization(): Result<Unit> {
        return try {
            dataStore.edit { preferences ->
                preferences.clear()
            }
            Log.d(TAG, "Customization data cleared successfully")
            Result.success(Unit)
        } catch (exception: Exception) {
            Log.e(TAG, "Error clearing customization data", exception)
            Result.failure(exception)
        }
    }
    
    override suspend fun hasCustomization(): Boolean {
        return try {
            val preferences = dataStore.data.first()
            preferences.contains(SELECTED_STYLE_ID) && 
            preferences[SELECTED_STYLE_ID]?.isNotBlank() == true
        } catch (exception: Exception) {
            Log.e(TAG, "Error checking customization existence", exception)
            false
        }
    }
    
    override suspend fun getLastModifiedTimestamp(): Long {
        return try {
            val preferences = dataStore.data.first()
            preferences[LAST_MODIFIED_TIMESTAMP] ?: 0L
        } catch (exception: Exception) {
            Log.e(TAG, "Error getting last modified timestamp", exception)
            0L
        }
    }
    
    /**
     * Maps DataStore preferences to CustomizationConfig.
     */
    private fun mapPreferencesToConfig(preferences: Preferences): CustomizationConfig {
        val batteryStyleConfig = BatteryStyleConfig(
            showEmoji = preferences[SHOW_EMOJI] ?: true,
            showPercentage = preferences[SHOW_PERCENTAGE] ?: true,
            percentageFontSizeDp = preferences[PERCENTAGE_FONT_SIZE] ?: 14,
            emojiSizeScale = preferences[EMOJI_SIZE_SCALE] ?: 1.0f,
            percentageColor = preferences[PERCENTAGE_COLOR] ?: 0xFFFFFFFF.toInt()
        )
        
        return CustomizationConfig(
            selectedStyleId = preferences[SELECTED_STYLE_ID] ?: "",
            customConfig = batteryStyleConfig,
            isGlobalEnabled = preferences[IS_GLOBAL_ENABLED] ?: false,
            lastModifiedTimestamp = preferences[LAST_MODIFIED_TIMESTAMP] ?: System.currentTimeMillis()
        )
    }
}
