package com.tqhit.battery.one.features.emoji.presentation.overlay

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.utils.BatteryLogger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.math.abs
import kotlin.math.max

/**
 * Custom view for displaying emoji battery overlay.
 * Implements horizontal bar layout with left and right groups as specified in overlayui.md
 * 
 * Layout Structure:
 * - Main Container: Horizontal bar with rounded corners and white background
 * - Left Group: Time, Silent Mode, Emoji (aligned to start)
 * - Right Group: Cellular Signal, WiFi Signal, Battery Indicator (aligned to end)
 * - Battery Indicator: Composite view with battery outline, fill, heart icon, and percentage text
 * 
 * This view follows the established patterns in the app:
 * - Uses custom drawing with Canvas and Paint
 * - Integrates with existing battery status data
 * - Supports customization configuration
 * - Follows Material 3 design guidelines
 * - Optimized for overlay display performance
 * - Supports swipe down gesture for notification panel
 */
class EmojiBatteryView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    
    companion object {
        private const val TAG = "EmojiBatteryView"
        private const val EMOJI_VIEW_TAG = "EmojiView_Rendering"
        private const val EMOJI_DRAW_TAG = "EmojiView_Drawing"
        private const val EMOJI_UPDATE_TAG = "EmojiView_Updates"
        private const val EMOJI_GESTURE_TAG = "EmojiView_Gesture"

        // Note: All dimensions and colors now moved to XML resources for better maintainability
    }
    
    // Paint objects for drawing
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        typeface = Typeface.DEFAULT_BOLD
        textAlign = Paint.Align.LEFT
        color = ContextCompat.getColor(context, R.color.emoji_text_color)
    }

    private val emojiPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        typeface = Typeface.DEFAULT
        textAlign = Paint.Align.LEFT
        textSize = resources.getDimension(R.dimen.emoji_overlay_emoji_size)
    }

    private val batteryPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
        color = ContextCompat.getColor(context, R.color.emoji_battery_percentage)
    }

    private val batteryStrokePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.STROKE
        strokeWidth = 2f
        color = ContextCompat.getColor(context, R.color.emoji_battery_outline)
    }

    private val heartPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
        color = ContextCompat.getColor(context, R.color.emoji_heart_icon)
        alpha = 180  // Semi-transparent
    }
    
    // Dimensions (loaded from resources)
    private val defaultHeight = resources.getDimensionPixelSize(R.dimen.emoji_overlay_height)
    private val defaultEmojiSize = resources.getDimensionPixelSize(R.dimen.emoji_overlay_emoji_size)
    private val defaultBatteryWidth = resources.getDimensionPixelSize(R.dimen.emoji_overlay_battery_width)
    private val defaultBatteryHeight = resources.getDimensionPixelSize(R.dimen.emoji_overlay_battery_height)
    private val elementSpacing = resources.getDimensionPixelSize(R.dimen.emoji_overlay_element_spacing)
    private val horizontalPadding = resources.getDimensionPixelSize(R.dimen.emoji_overlay_horizontal_padding)
    private val groupSpacing = resources.getDimensionPixelSize(R.dimen.emoji_overlay_group_spacing)
    
    // Current state
    private var batteryStatus: CoreBatteryStatus? = null
    private var batteryStyle: BatteryStyle? = null
    private var customizationConfig: CustomizationConfig? = null
    private var statusBarInfo: CustomStatusBarInfo? = null
    
    // Cached bitmaps for performance
    private var emojiBitmap: Bitmap? = null
    private var batteryBitmap: Bitmap? = null

    // Image loading dependencies
    private var getBatteryStylesUseCase: GetBatteryStylesUseCase? = null
    private val coroutineScope = CoroutineScope(Dispatchers.Main)
    
    // Drawing rectangles for battery indicator
    private val batteryRect = RectF()
    private val batteryFillRect = RectF()
    private val batteryTerminalRect = RectF()
    private val heartRect = RectF()
    
    // Advanced gesture detection using helper class
    private val emojiGestureHelper = EmojiGestureHelper()

    // Gesture detection for advanced swipe down with left/right half detection
    private val gestureDetector = GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {
        override fun onFling(e1: MotionEvent?, e2: MotionEvent, velocityX: Float, velocityY: Float): Boolean {
            BatteryLogger.d(EMOJI_GESTURE_TAG, "Fling gesture detected - analyzing with gesture helper")

            // Get screen width for position calculations
            val screenWidth = resources.displayMetrics.widthPixels

            // Use gesture helper to analyze the gesture
            val gestureResult = emojiGestureHelper.analyzeGesture(e1, e2, velocityX, velocityY, screenWidth)

            // Handle the gesture based on the result
            return handleGestureResult(gestureResult)
        }
    })
    
    init {
        setupDefaultColors()
        setupBackground()
        BatteryLogger.d(TAG, "EmojiBatteryView initialized with horizontal bar layout")
        BatteryLogger.d(EMOJI_VIEW_TAG, "EMOJI_VIEW_CREATED")
    }

    /**
     * Sets up the background drawable for status bar integration
     */
    private fun setupBackground() {
        BatteryLogger.d(EMOJI_VIEW_TAG, "Setting up emoji overlay background")
        BatteryLogger.d(EMOJI_VIEW_TAG, "EMOJI_BACKGROUND_SETUP_STARTED")

        // Detect current theme
        val currentNightMode = resources.configuration.uiMode and android.content.res.Configuration.UI_MODE_NIGHT_MASK
        val isDarkTheme = currentNightMode == android.content.res.Configuration.UI_MODE_NIGHT_YES
        BatteryLogger.d(EMOJI_VIEW_TAG, "Theme detection - isDarkTheme: $isDarkTheme, nightMode: $currentNightMode")

        // Load background drawable that automatically adapts to light/dark theme
        val backgroundDrawable = ContextCompat.getDrawable(context, R.drawable.emoji_overlay_background)
        BatteryLogger.d(EMOJI_VIEW_TAG, "Background drawable loaded: ${backgroundDrawable != null}")

        if (backgroundDrawable != null) {
            background = backgroundDrawable
            BatteryLogger.d(EMOJI_VIEW_TAG, "Background drawable applied successfully")
            BatteryLogger.d(EMOJI_VIEW_TAG, "Background drawable type: ${backgroundDrawable.javaClass.simpleName}")
        } else {
            BatteryLogger.e(EMOJI_VIEW_TAG, "Failed to load background drawable!")
            BatteryLogger.e(EMOJI_VIEW_TAG, "EMOJI_BACKGROUND_LOAD_FAILED")
        }

        // Set elevation for status bar integration
        val elevationPx = 4f * resources.displayMetrics.density
        elevation = elevationPx
        BatteryLogger.d(EMOJI_VIEW_TAG, "Elevation set to: ${elevationPx}px (4dp)")

        // Add padding for better visual separation from background
        val paddingHorizontal = resources.getDimensionPixelSize(R.dimen.emoji_overlay_horizontal_padding)
        val paddingVertical = (4 * resources.displayMetrics.density).toInt()
        setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical)
        BatteryLogger.d(EMOJI_VIEW_TAG, "Padding applied - horizontal: ${paddingHorizontal}px, vertical: ${paddingVertical}px")

        // Verify background is set
        val currentBackground = background
        BatteryLogger.d(EMOJI_VIEW_TAG, "Background verification - current background: ${currentBackground != null}")
        if (currentBackground != null) {
            BatteryLogger.d(EMOJI_VIEW_TAG, "Background bounds: ${currentBackground.bounds}")
            BatteryLogger.d(EMOJI_VIEW_TAG, "Background alpha: ${currentBackground.alpha}")
        }

        BatteryLogger.d(EMOJI_VIEW_TAG, "EMOJI_BACKGROUND_SETUP_COMPLETED")
    }

    /**
     * Helper method to determine if the current theme is dark
     */
    private fun isDarkTheme(): Boolean {
        val nightModeFlags = resources.configuration.uiMode and android.content.res.Configuration.UI_MODE_NIGHT_MASK
        return nightModeFlags == android.content.res.Configuration.UI_MODE_NIGHT_YES
    }

    /**
     * Sets up default colors based on theme for proper contrast and accessibility
     */
    private fun setupDefaultColors() {
        BatteryLogger.d(EMOJI_VIEW_TAG, "Setting up emoji view colors with dynamic contrast for full-width design")

        // Detect current theme for proper contrast
        val currentNightMode = resources.configuration.uiMode and android.content.res.Configuration.UI_MODE_NIGHT_MASK
        val isDarkTheme = currentNightMode == android.content.res.Configuration.UI_MODE_NIGHT_YES
        
        // Define colors based on theme for proper contrast
        val iconColor = if (isDarkTheme) {
            Color.WHITE  // Light icons on dark background
        } else {
            Color.BLACK  // Dark icons on light background
        }
        
        val accentColor = ContextCompat.getColor(context, R.color.emoji_battery_percentage)  // Keep brand color for battery
        
        BatteryLogger.d(EMOJI_VIEW_TAG, "Theme: ${if (isDarkTheme) "Dark" else "Light"}, Icon color: ${Integer.toHexString(iconColor)}")

        // Configure text paint for time and percentage with theme-appropriate colors
        textPaint.color = iconColor  // Dynamic based on theme
        textPaint.textSize = resources.getDimension(R.dimen.emoji_overlay_text_size)
        textPaint.typeface = Typeface.DEFAULT_BOLD

        // Configure battery indicator colors (keep brand colors for visual identity)
        batteryPaint.color = accentColor
        batteryStrokePaint.color = iconColor  // Dynamic outline color
        heartPaint.color = ContextCompat.getColor(context, R.color.emoji_heart_icon)

        val batteryPercentageColorHex = Integer.toHexString(accentColor)
        BatteryLogger.d(EMOJI_VIEW_TAG, "Dynamic colors set - icons: ${Integer.toHexString(iconColor)}, battery: $batteryPercentageColorHex, stroke: ${Integer.toHexString(iconColor)}")
    }
    
    /**
     * Handles the result of advanced gesture analysis for Task 4.2 implementation.
     *
     * This method processes gesture results from EmojiGestureHelper and triggers
     * appropriate system actions through the NotificationActionCallback interface.
     *
     * **Gesture Processing:**
     * - SWIPE_DOWN_LEFT → Request notification panel via callback
     * - SWIPE_DOWN_RIGHT → Request quick settings panel via callback
     * - SWIPE_DOWN_CENTER → Request notification panel (default) via callback
     * - NONE → No action taken, gesture ignored
     *
     * **Architecture:**
     * - Uses callback pattern to communicate with accessibility service
     * - Falls back to legacy notification panel if no callback is set
     * - Comprehensive logging for debugging and monitoring
     * - Returns boolean to indicate if gesture was handled
     *
     * @param gestureResult The result from EmojiGestureHelper analysis containing gesture type and details
     * @return true if the gesture was handled and action was requested, false otherwise
     * @see EmojiGestureHelper.GestureResult
     * @see NotificationActionCallback.onNotificationActionRequested
     */
    private fun handleGestureResult(gestureResult: EmojiGestureHelper.GestureResult): Boolean {
        BatteryLogger.d(EMOJI_GESTURE_TAG, "Handling gesture result: ${gestureResult.gestureType}")

        when (gestureResult.gestureType) {
            EmojiGestureHelper.GestureType.SWIPE_DOWN_LEFT -> {
                BatteryLogger.d(EMOJI_GESTURE_TAG, "Left swipe detected - requesting notification panel")
                requestNotificationAction(EmojiGestureHelper.GestureType.SWIPE_DOWN_LEFT)
                return true
            }
            EmojiGestureHelper.GestureType.SWIPE_DOWN_RIGHT -> {
                BatteryLogger.d(EMOJI_GESTURE_TAG, "Right swipe detected - requesting quick settings")
                requestNotificationAction(EmojiGestureHelper.GestureType.SWIPE_DOWN_RIGHT)
                return true
            }
            EmojiGestureHelper.GestureType.SWIPE_DOWN_CENTER -> {
                BatteryLogger.d(EMOJI_GESTURE_TAG, "Center swipe detected - requesting notification panel (default)")
                requestNotificationAction(EmojiGestureHelper.GestureType.SWIPE_DOWN_CENTER)
                return true
            }
            EmojiGestureHelper.GestureType.NONE -> {
                BatteryLogger.d(EMOJI_GESTURE_TAG, "No valid gesture detected")
                return false
            }
        }
    }

    /**
     * Callback interface for advanced gesture communication with accessibility service.
     *
     * This interface enables EmojiBatteryView to communicate gesture detection results
     * to the EmojiBatteryAccessibilityService for Task 4.2 implementation.
     *
     * **Design Pattern:**
     * - Implements callback pattern for loose coupling between view and service
     * - Allows view to remain independent of accessibility service implementation
     * - Enables testability by allowing mock implementations
     * - Supports future extensibility for additional gesture types
     *
     * **Usage:**
     * - Set via EmojiBatteryView.setNotificationActionCallback()
     * - Called from handleGestureResult() when valid gestures are detected
     * - Implemented by EmojiBatteryAccessibilityService.onNotificationActionRequested()
     *
     * @see EmojiBatteryAccessibilityService.onNotificationActionRequested
     * @see EmojiGestureHelper.GestureType
     */
    interface NotificationActionCallback {
        /**
         * Called when a gesture requires a notification action to be performed.
         *
         * @param gestureType The type of gesture that was detected and should trigger an action
         */
        fun onNotificationActionRequested(gestureType: EmojiGestureHelper.GestureType)
    }

    // Callback for communicating with the accessibility service
    private var notificationActionCallback: NotificationActionCallback? = null

    /**
     * Sets the callback for notification actions.
     * This allows the view to communicate with the accessibility service.
     */
    fun setNotificationActionCallback(callback: NotificationActionCallback?) {
        this.notificationActionCallback = callback
        BatteryLogger.d(EMOJI_GESTURE_TAG, "Notification action callback set: ${callback != null}")
    }

    /**
     * Requests a notification action through the callback.
     * Falls back to legacy method if no callback is set.
     */
    private fun requestNotificationAction(gestureType: EmojiGestureHelper.GestureType) {
        BatteryLogger.d(EMOJI_GESTURE_TAG, "Requesting notification action for gesture: $gestureType")

        if (notificationActionCallback != null) {
            BatteryLogger.d(EMOJI_GESTURE_TAG, "Using callback to request notification action")
            notificationActionCallback?.onNotificationActionRequested(gestureType)
        } else {
            BatteryLogger.w(EMOJI_GESTURE_TAG, "No callback set - falling back to legacy notification panel")
            openNotificationPanelLegacy()
        }
    }

    /**
     * Legacy method for opening notification panel using system intent.
     * Kept for backward compatibility when accessibility service is not available.
     */
    private fun openNotificationPanelLegacy() {
        BatteryLogger.d(EMOJI_GESTURE_TAG, "Opening notification panel via legacy system intent")
        try {
            // Use system intent to open notification panel
            val intent = context.packageManager.getLaunchIntentForPackage("com.android.systemui")
            if (intent != null) {
                intent.action = "android.intent.action.OPEN_NOTIFICATION_PANEL"
                context.startActivity(intent)
                BatteryLogger.d(EMOJI_GESTURE_TAG, "Legacy notification panel intent sent")
            } else {
                BatteryLogger.w(EMOJI_GESTURE_TAG, "Could not create legacy notification panel intent")
            }
        } catch (exception: Exception) {
            BatteryLogger.e(EMOJI_GESTURE_TAG, "Error opening legacy notification panel", exception)
        }
    }

    /**
     * Updates the battery status and triggers a redraw
     */
    fun updateBatteryStatus(status: CoreBatteryStatus) {
        BatteryLogger.d(EMOJI_UPDATE_TAG, "Updating emoji battery status: ${status.percentage}%, charging: ${status.isCharging}")
        BatteryLogger.logBatteryStatus(
            EMOJI_UPDATE_TAG,
            status.percentage,
            status.isCharging,
            status.currentMicroAmperes,
            status.voltageMillivolts,
            status.temperatureCelsius
        )

        this.batteryStatus = status
        invalidate()
        BatteryLogger.d(EMOJI_UPDATE_TAG, "EMOJI_BATTERY_STATUS_UPDATED")
    }

    /**
     * Updates the battery style and triggers a redraw
     */
    fun updateBatteryStyle(style: BatteryStyle) {
        BatteryLogger.d(EMOJI_UPDATE_TAG, "Updating emoji battery style: ${style.name}")
        this.batteryStyle = style

        // Clear cached bitmaps to force reload
        emojiBitmap = null
        batteryBitmap = null

        invalidate()
        BatteryLogger.d(EMOJI_UPDATE_TAG, "EMOJI_BATTERY_STYLE_UPDATED: ${style.name}")
    }

    /**
     * Sets the GetBatteryStylesUseCase dependency for loading battery and emoji images
     */
    fun setGetBatteryStylesUseCase(useCase: GetBatteryStylesUseCase) {
        this.getBatteryStylesUseCase = useCase
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: GetBatteryStylesUseCase dependency set")
    }

    /**
     * Updates the customization configuration and triggers a redraw
     */
    fun updateCustomizationConfig(config: CustomizationConfig) {
        BatteryLogger.d(EMOJI_UPDATE_TAG, "Updating emoji customization config")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: ========== VIEW CONFIG UPDATE STARTED ==========")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: New config - selectedStyleId: ${config.selectedStyleId}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: New config - showEmoji: ${config.customConfig.showEmoji}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: New config - showPercentage: ${config.customConfig.showPercentage}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: New config - percentageFontSize: ${config.customConfig.percentageFontSizeDp}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: New config - emojiSizeScale: ${config.customConfig.emojiSizeScale}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: New config - percentageColor: ${config.customConfig.percentageColor}")

        this.customizationConfig = config
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: Updated customizationConfig field")

        // Update paint properties based on config
        val oldTextColor = textPaint.color
        val oldTextSize = textPaint.textSize
        textPaint.color = config.customConfig.percentageColor
        textPaint.textSize = config.customConfig.percentageFontSizeDp * resources.displayMetrics.scaledDensity
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: Updated paint - color: $oldTextColor -> ${textPaint.color}, size: $oldTextSize -> ${textPaint.textSize}")

        BatteryLogger.d(EMOJI_UPDATE_TAG, "Config updated - show emoji: ${config.customConfig.showEmoji}, show percentage: ${config.customConfig.showPercentage}")

        // Load images for the new configuration
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: Loading images for new configuration")
        loadImagesForConfiguration(config)

        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: Calling invalidate() to trigger redraw")
        invalidate()
        BatteryLogger.d(EMOJI_UPDATE_TAG, "EMOJI_CUSTOMIZATION_CONFIG_UPDATED")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "VIEW_DEBUG: ========== VIEW CONFIG UPDATE COMPLETED ==========")
    }

    /**
     * Updates the custom status bar information and triggers a redraw
     */
    fun updateStatusBarInfo(info: CustomStatusBarInfo) {
        BatteryLogger.d(TAG, "Updating custom status bar info: time=${info.currentTime}, wifi=${info.wifiSignalStrength}, cellular=${info.cellularSignalStrength}")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "STATUS_BAR_INFO: Time=${info.currentTime}, WiFi=${info.hasWifiConnection}, Cellular=${info.hasCellularConnection}, Silent=${info.isRingerSilent}")

        this.statusBarInfo = info
        invalidate()
        BatteryLogger.d(EMOJI_UPDATE_TAG, "EMOJI_STATUS_BAR_INFO_UPDATED")
    }

    /**
     * Updates background and colors when theme changes
     */
    fun updateTheme() {
        BatteryLogger.d(TAG, "Updating emoji overlay theme")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "EMOJI_THEME_UPDATE_STARTED")

        // Force clear current background before applying new one
        background = null
        BatteryLogger.d(EMOJI_UPDATE_TAG, "Background cleared for theme update")

        // Refresh background drawable for new theme
        setupBackground()
        setupDefaultColors()

        // Force layout and redraw with new theme
        requestLayout()
        invalidate()

        BatteryLogger.d(EMOJI_UPDATE_TAG, "EMOJI_THEME_UPDATE_COMPLETED")
    }

    /**
     * Forces background refresh - useful for debugging background issues
     */
    fun forceBackgroundRefresh() {
        BatteryLogger.d(EMOJI_UPDATE_TAG, "EMOJI_BACKGROUND_FORCE_REFRESH_STARTED")

        // Clear current background
        background = null

        // Reapply background
        setupBackground()

        // Force redraw
        invalidate()

        BatteryLogger.d(EMOJI_UPDATE_TAG, "EMOJI_BACKGROUND_FORCE_REFRESH_COMPLETED")
    }
    
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        BatteryLogger.d(EMOJI_VIEW_TAG, "VIEW_MEASURE_STARTED")
        
        val config = customizationConfig ?: CustomizationConfig.createDefault()
        val status = batteryStatus ?: CoreBatteryStatus.createDefault()

        // For full-width overlay, use the entire available screen width
        val screenWidth = resources.displayMetrics.widthPixels
        val requiredHeight = defaultHeight

        BatteryLogger.d(EMOJI_VIEW_TAG, "VIEW_MEASURE_SCREEN_WIDTH: ${screenWidth}px")
        BatteryLogger.d(EMOJI_VIEW_TAG, "VIEW_MEASURE_REQUIRED_HEIGHT: ${requiredHeight}px")
        BatteryLogger.d(EMOJI_VIEW_TAG, "VIEW_MEASURE_PADDING: horizontal=${horizontalPadding}px, vertical=${paddingTop}px")

        // Calculate minimum required width for all elements
        var minRequiredWidth = horizontalPadding * 2

        // Left group minimum width (Time + Emoji)  
        var leftGroupMinWidth = 0
        leftGroupMinWidth += measureTimeWidth().toInt() + elementSpacing
        if (config.customConfig.showEmoji) {
            leftGroupMinWidth += (defaultEmojiSize * config.customConfig.emojiSizeScale).toInt() + elementSpacing
        }

        // Right group minimum width (WiFi + Cellular + Silent + Battery)
        var rightGroupMinWidth = 0
        rightGroupMinWidth += measureWifiIconWidth().toInt() + elementSpacing
        rightGroupMinWidth += measureCellularIconWidth().toInt() + elementSpacing  
        rightGroupMinWidth += measureSilentIconWidth().toInt() + elementSpacing
        rightGroupMinWidth += defaultBatteryWidth
        if (config.customConfig.showPercentage) {
            rightGroupMinWidth += measurePercentageWidth(status).toInt() + elementSpacing
        }

        minRequiredWidth += leftGroupMinWidth + rightGroupMinWidth + groupSpacing

        // Use full screen width if available, or minimum required width
        val finalWidth = max(screenWidth, minRequiredWidth)
        val finalHeight = resolveSize(requiredHeight, heightMeasureSpec)

        BatteryLogger.d(EMOJI_VIEW_TAG, "VIEW_MEASURE_CALCULATIONS: leftGroup=${leftGroupMinWidth}px, rightGroup=${rightGroupMinWidth}px, minRequired=${minRequiredWidth}px")
        BatteryLogger.d(EMOJI_VIEW_TAG, "VIEW_MEASURE_FINAL_DIMENSIONS: ${finalWidth}x${finalHeight}px")
        BatteryLogger.d(EMOJI_VIEW_TAG, "VIEW_MEASURE_COMPLETED")

        setMeasuredDimension(finalWidth, finalHeight)
    }
    
    override fun onDraw(canvas: Canvas) {
        // Log background state before drawing
        val currentBackground = background
        BatteryLogger.v(EMOJI_DRAW_TAG, "Pre-draw background check - background: ${currentBackground != null}")
        if (currentBackground != null) {
            BatteryLogger.v(EMOJI_DRAW_TAG, "Background visible: ${currentBackground.isVisible}, alpha: ${currentBackground.alpha}")
        }

        super.onDraw(canvas)

        BatteryLogger.v(EMOJI_DRAW_TAG, "Drawing emoji battery view with full-width layout")
        BatteryLogger.v(EMOJI_DRAW_TAG, "View dimensions: ${width}x${height}, padding: ${paddingLeft},${paddingTop},${paddingRight},${paddingBottom}")

        val config = customizationConfig ?: CustomizationConfig.createDefault()
        val status = batteryStatus ?: CoreBatteryStatus.createDefault()
        val statusInfo = statusBarInfo ?: CustomStatusBarInfo.fromSystemState(context)

        val centerY = height / 2f

        BatteryLogger.v(EMOJI_DRAW_TAG, "Drawing full-width elements - emoji: ${config.customConfig.showEmoji}, percentage: ${config.customConfig.showPercentage}, battery: ${status.percentage}%")

        // Full-width layout: distribute elements across entire width
        drawFullWidthLayout(canvas, centerY, statusInfo, status, config)

        BatteryLogger.v(EMOJI_DRAW_TAG, "EMOJI_VIEW_DRAW_COMPLETED")
    }

    /**
     * Draws the full-width layout with proper element distribution
     */
    private fun drawFullWidthLayout(canvas: Canvas, centerY: Float, statusInfo: CustomStatusBarInfo, status: CoreBatteryStatus, config: CustomizationConfig) {
        val totalWidth = width.toFloat()
        val leftMargin = horizontalPadding.toFloat()
        val rightMargin = horizontalPadding.toFloat()
        val availableWidth = totalWidth - leftMargin - rightMargin

        BatteryLogger.d(EMOJI_DRAW_TAG, "Starting full-width layout - totalWidth: $totalWidth, available: $availableWidth")

        // Calculate widths for each section - NEW LAYOUT: time+emoji on left, all status icons on right
        val leftGroupWidth = calculateNewLeftGroupWidth(statusInfo, config)
        val rightGroupWidth = calculateNewRightGroupWidth(statusInfo, status, config)
        val centerSpaceWidth = availableWidth - leftGroupWidth - rightGroupWidth

        BatteryLogger.d(EMOJI_DRAW_TAG, "Layout distribution - left: $leftGroupWidth, center: $centerSpaceWidth, right: $rightGroupWidth")

        // Draw left group (Time + Emoji) - aligned to left
        var currentX = leftMargin
        currentX += drawNewLeftGroup(canvas, currentX, centerY, statusInfo, config)

        // Draw right group (All status icons: WiFi + Cellular + Ring/Silent + Battery) - aligned to right  
        val rightStartX = totalWidth - rightMargin - rightGroupWidth
        drawNewRightGroup(canvas, rightStartX, centerY, statusInfo, status, config)
        
        BatteryLogger.d(EMOJI_DRAW_TAG, "Layout completed - left group at $leftMargin, right group at $rightStartX")
    }

    /**
     * Calculates the width required for the NEW left group (Time + Emoji)
     */
    private fun calculateNewLeftGroupWidth(statusInfo: CustomStatusBarInfo, config: CustomizationConfig): Float {
        var width = 0f
        // Time width
        width += measureTimeWidth() + elementSpacing
        // Emoji width (if enabled)
        if (config.customConfig.showEmoji) {
            width += (defaultEmojiSize * config.customConfig.emojiSizeScale).toInt() + elementSpacing
        }
        BatteryLogger.d(EMOJI_DRAW_TAG, "Left group width calculation: time + emoji = $width")
        return width
    }

    /**
     * Calculates the width required for the NEW right group (All status icons)
     */
    private fun calculateNewRightGroupWidth(statusInfo: CustomStatusBarInfo, status: CoreBatteryStatus, config: CustomizationConfig): Float {
        var width = 0f
        // WiFi icon
        if (statusInfo.hasWifiConnection) {
            width += measureWifiIconWidth() + elementSpacing
        }
        // Cellular icon  
        if (statusInfo.hasCellularConnection) {
            width += measureCellularIconWidth() + elementSpacing
        }
        // Ring/Silent icon
        if (statusInfo.isRingerSilent) {
            width += measureSilentIconWidth() + elementSpacing
        }
        // Battery indicator
        width += defaultBatteryWidth
        // Battery percentage (if enabled)
        if (config.customConfig.showPercentage) {
            width += measurePercentageWidth(status) + elementSpacing
        }
        BatteryLogger.d(EMOJI_DRAW_TAG, "Right group width calculation: all status icons = $width")
        return width
    }

    /**
     * Draws the NEW left group (Time + Emoji)
     */
    private fun drawNewLeftGroup(canvas: Canvas, x: Float, centerY: Float, statusInfo: CustomStatusBarInfo, config: CustomizationConfig): Float {
        var currentX = x
        BatteryLogger.d(EMOJI_DRAW_TAG, "Drawing new left group starting at x=$currentX")

        // Draw time (leftmost element)
        val timeWidth = drawTime(canvas, currentX, centerY, statusInfo.currentTime)
        currentX += timeWidth + elementSpacing
        BatteryLogger.d(EMOJI_DRAW_TAG, "Drew time, moved to x=$currentX")

        // Draw emoji (next to time)
        if (config.customConfig.showEmoji) {
            val emojiWidth = drawEmoji(canvas, currentX, centerY, config)
            currentX += emojiWidth + elementSpacing
            BatteryLogger.d(EMOJI_DRAW_TAG, "Drew emoji, moved to x=$currentX")
        }

        val totalWidth = currentX - x
        BatteryLogger.d(EMOJI_DRAW_TAG, "Left group completed, total width: $totalWidth")
        return totalWidth
    }

    /**
     * Draws the NEW right group (All status icons: WiFi + Cellular + Ring/Silent + Battery)
     */
    private fun drawNewRightGroup(canvas: Canvas, x: Float, centerY: Float, statusInfo: CustomStatusBarInfo, status: CoreBatteryStatus, config: CustomizationConfig): Float {
        var currentX = x
        BatteryLogger.d(EMOJI_DRAW_TAG, "Drawing new right group starting at x=$currentX")

        // Draw WiFi signal (first in right group)
        if (statusInfo.hasWifiConnection) {
            val wifiWidth = drawWifiIcon(canvas, currentX, centerY, statusInfo.wifiSignalStrength)
            currentX += wifiWidth + elementSpacing
            BatteryLogger.d(EMOJI_DRAW_TAG, "Drew WiFi icon, moved to x=$currentX")
        }

        // Draw cellular signal
        if (statusInfo.hasCellularConnection) {
            val cellularWidth = drawCellularIcon(canvas, currentX, centerY, statusInfo.cellularSignalStrength)
            currentX += cellularWidth + elementSpacing
            BatteryLogger.d(EMOJI_DRAW_TAG, "Drew cellular icon, moved to x=$currentX")
        }

        // Draw ring/silent mode icon
        if (statusInfo.isRingerSilent) {
            val silentWidth = drawSilentModeIcon(canvas, currentX, centerY)
            currentX += silentWidth + elementSpacing
            BatteryLogger.d(EMOJI_DRAW_TAG, "Drew silent mode icon, moved to x=$currentX")
        }

        // Draw battery indicator (rightmost element)
        val batteryWidth = drawBatteryIndicator(canvas, currentX, centerY, status, config)
        currentX += batteryWidth
        BatteryLogger.d(EMOJI_DRAW_TAG, "Drew battery indicator, final x=$currentX")

        val totalWidth = currentX - x
        BatteryLogger.d(EMOJI_DRAW_TAG, "Right group completed, total width: $totalWidth")
        return totalWidth
    }

    /**
     * Draws the time display with theme-appropriate colors
     */
    private fun drawTime(canvas: Canvas, x: Float, centerY: Float, time: String): Float {
        // Use the already configured textPaint which has theme-appropriate colors
        val timePaint = Paint(textPaint).apply {
            textSize = (12 * resources.displayMetrics.scaledDensity) // Slightly larger for full-width layout
            typeface = Typeface.DEFAULT_BOLD
            textAlign = Paint.Align.LEFT
        }

        // Calculate text bounds for positioning
        val textBounds = Rect()
        timePaint.getTextBounds(time, 0, time.length, textBounds)

        val textY = centerY + (textBounds.height() / 2f) - textBounds.bottom
        canvas.drawText(time, x, textY, timePaint)

        BatteryLogger.v(EMOJI_DRAW_TAG, "Drew time '$time' at ($x, $textY) with theme-appropriate color")
        return textBounds.width().toFloat()
    }

    /**
     * Draws the silent mode icon with theme-appropriate colors
     */
    private fun drawSilentModeIcon(canvas: Canvas, x: Float, centerY: Float): Float {
        return drawStatusIcon(
            canvas = canvas,
            x = x,
            centerY = centerY,
            drawableResourceId = R.drawable.ic_custom_ring_silent,
            iconType = "SILENT",
            signalStrength = null
        )
    }
    
    /**
     * Draws the emoji at the specified position using actual emoji character
     */
    private fun drawEmoji(canvas: Canvas, x: Float, centerY: Float, config: CustomizationConfig): Float {
        val emojiSize = (defaultEmojiSize * config.customConfig.emojiSizeScale).toInt()

        // Get emoji character from battery style or use default laughing emoji from screenshot
        val emojiCharacter = getEmojiCharacter(config)

        // Configure emoji paint with proper size
        emojiPaint.textSize = emojiSize.toFloat()

        // Calculate text bounds for proper positioning
        val textBounds = Rect()
        emojiPaint.getTextBounds(emojiCharacter, 0, emojiCharacter.length, textBounds)

        // Calculate position to center emoji vertically
        val emojiX = x
        val emojiY = centerY + (textBounds.height() / 2f) - textBounds.bottom

        // Draw the actual emoji character
        canvas.drawText(emojiCharacter, emojiX, emojiY, emojiPaint)

        BatteryLogger.v(EMOJI_DRAW_TAG, "Drew emoji '$emojiCharacter' at ($emojiX, $emojiY) with size $emojiSize")

        return textBounds.width().toFloat()
    }

    /**
     * Loads battery and emoji images for the given configuration
     */
    private fun loadImagesForConfiguration(config: CustomizationConfig) {
        BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: ========== LOADING IMAGES FOR CONFIG ==========")
        BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: selectedStyleId: ${config.selectedStyleId}")

        if (config.selectedStyleId.isBlank()) {
            BatteryLogger.w(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: selectedStyleId is blank, cannot load images")
            return
        }

        val useCase = getBatteryStylesUseCase
        if (useCase == null) {
            BatteryLogger.w(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: GetBatteryStylesUseCase is null, cannot load images")
            return
        }

        // Parse composite style ID (e.g., "emoji-3_emoji-8")
        val parts = config.selectedStyleId.split("_")
        if (parts.size != 2) {
            BatteryLogger.w(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Invalid composite style ID format: ${config.selectedStyleId}")
            return
        }

        val batteryStyleId = parts[0] // e.g., "emoji-3"
        val emojiStyleId = parts[1]   // e.g., "emoji-8"

        BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Parsed IDs - battery: $batteryStyleId, emoji: $emojiStyleId")

        // Load images asynchronously
        coroutineScope.launch {
            try {
                val allStyles = useCase.getAllStyles()
                BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Loaded ${allStyles.size} styles")

                // Find battery and emoji styles
                val batteryStyle = allStyles.find { it.id == batteryStyleId }
                val emojiStyle = allStyles.find { it.id == emojiStyleId }

                BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Found battery style: ${batteryStyle != null}")
                BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Found emoji style: ${emojiStyle != null}")

                if (batteryStyle != null) {
                    loadBatteryImage(batteryStyle.batteryImageUrl)
                }

                if (emojiStyle != null) {
                    loadEmojiImage(emojiStyle.emojiImageUrl)
                }

            } catch (exception: Exception) {
                BatteryLogger.e(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: Error loading styles", exception)
            }
        }

        BatteryLogger.d(EMOJI_UPDATE_TAG, "LOAD_IMAGES_DEBUG: ========== LOADING IMAGES INITIATED ==========")
    }

    /**
     * Gets the emoji character to display based on configuration
     */
    private fun getEmojiCharacter(config: CustomizationConfig): String {
        // Use laughing emoji from screenshot as default
        // In future, this can be customized based on battery style configuration
        return "😂"  // Laughing emoji as shown in screenshot
    }

    /**
     * Draws WiFi signal strength icon with theme-appropriate colors
     */
    private fun drawWifiIcon(canvas: Canvas, x: Float, centerY: Float, signalStrength: Int): Float {
        return drawStatusIcon(
            canvas = canvas,
            x = x,
            centerY = centerY,
            drawableResourceId = R.drawable.ic_custom_wifi,
            iconType = "WIFI",
            signalStrength = signalStrength
        )
    }

    /**
     * Draws cellular signal strength icon with theme-appropriate colors
     */
    private fun drawCellularIcon(canvas: Canvas, x: Float, centerY: Float, signalStrength: Int): Float {
        return drawStatusIcon(
            canvas = canvas,
            x = x,
            centerY = centerY,
            drawableResourceId = R.drawable.ic_custom_cellular,
            iconType = "CELLULAR",
            signalStrength = signalStrength
        )
    }

    /**
     * Consolidated helper method for drawing status bar icons with consistent theming and positioning.
     * Eliminates code duplication between WiFi, cellular, and silent mode icons.
     *
     * @param canvas The canvas to draw on
     * @param x The x position for the icon
     * @param centerY The center y position for the icon
     * @param drawableResourceId The drawable resource ID for the icon
     * @param iconType The type of icon for logging purposes (e.g., "WIFI", "CELLULAR", "SILENT")
     * @param signalStrength Optional signal strength value for logging (used by WiFi and cellular icons)
     * @return The width of the drawn icon
     */
    private fun drawStatusIcon(
        canvas: Canvas,
        x: Float,
        centerY: Float,
        drawableResourceId: Int,
        iconType: String,
        signalStrength: Int? = null
    ): Float {
        val iconSize = (16 * resources.displayMetrics.density).toInt()  // Consistent 16dp size for all status icons
        BatteryLogger.d(EMOJI_DRAW_TAG, "CREATING_${iconType}_ICON: Starting to create ${iconType.lowercase()} icon")
        
        val drawable = ContextCompat.getDrawable(context, drawableResourceId)
        if (drawable == null) {
            BatteryLogger.e(EMOJI_DRAW_TAG, "CREATING_${iconType}_ICON: ERROR - Failed to load drawable resource")
            return iconSize.toFloat()
        }
        
        BatteryLogger.d(EMOJI_DRAW_TAG, "CREATING_${iconType}_ICON: Drawable loaded successfully")
        
        // Determine high-contrast color based on theme (consistent across all status icons)
        val isLightTheme = !isDarkTheme()
        val iconColor = if (isLightTheme) Color.BLACK else Color.WHITE
        val strengthLog = if (signalStrength != null) ", Strength=$signalStrength" else ""
        BatteryLogger.d(EMOJI_DRAW_TAG, "CREATING_${iconType}_ICON: Theme=${if (isLightTheme) "LIGHT" else "DARK"}, Color=${String.format("#%08X", iconColor)}$strengthLog")
        
        // Apply color filter for maximum contrast (consistent theming approach)
        drawable.setColorFilter(iconColor, android.graphics.PorterDuff.Mode.SRC_IN)
        BatteryLogger.d(EMOJI_DRAW_TAG, "CREATING_${iconType}_ICON: Color filter applied")
        
        // Set bounds for icon positioning (consistent bounds calculation)
        val bounds = android.graphics.Rect(
            x.toInt(),
            (centerY - iconSize / 2).toInt(),
            (x + iconSize).toInt(),
            (centerY + iconSize / 2).toInt()
        )
        drawable.setBounds(bounds.left, bounds.top, bounds.right, bounds.bottom)
        BatteryLogger.d(EMOJI_DRAW_TAG, "CREATING_${iconType}_ICON: Bounds set to ${bounds}")
        
        // Draw the icon to canvas
        drawable.draw(canvas)
        BatteryLogger.d(EMOJI_DRAW_TAG, "CREATING_${iconType}_ICON: Icon drawn successfully at position ($x, $centerY) with size $iconSize")

        return iconSize.toFloat()
    }

    /**
     * Draws the composite battery indicator with heart icon overlay
     */
    private fun drawBatteryIndicator(canvas: Canvas, x: Float, centerY: Float, status: CoreBatteryStatus, config: CustomizationConfig): Float {
        val batteryTop = centerY - defaultBatteryHeight / 2f
        
        // Layer 1: Battery outline
        batteryRect.set(x, batteryTop, x + defaultBatteryWidth, batteryTop + defaultBatteryHeight)
        canvas.drawRoundRect(batteryRect, 2f, 2f, batteryStrokePaint)
        
        // Layer 2: Battery fill based on level
        val fillWidth = defaultBatteryWidth * (status.percentage / 100f)
        batteryFillRect.set(x + 1, batteryTop + 1, x + fillWidth - 1, batteryTop + defaultBatteryHeight - 1)
        canvas.drawRoundRect(batteryFillRect, 1f, 1f, batteryPaint)
        
        // Layer 3: Battery terminal (small rectangle on the right)
        val terminalWidth = 2f
        val terminalHeight = defaultBatteryHeight * 0.4f
        val terminalTop = centerY - terminalHeight / 2f
        batteryTerminalRect.set(
            x + defaultBatteryWidth,
            terminalTop,
            x + defaultBatteryWidth + terminalWidth,
            terminalTop + terminalHeight
        )
        canvas.drawRect(batteryTerminalRect, batteryStrokePaint)
        
        // Layer 4: Heart icon overlay (centered on battery)
        val heartSize = defaultBatteryHeight * 0.8f
        val heartX = x + defaultBatteryWidth / 2f - heartSize / 2f
        val heartY = centerY - heartSize / 2f
        heartRect.set(heartX, heartY, heartX + heartSize, heartY + heartSize)
        
        // Draw heart shape using path
        val heartPath = Path()
        heartPath.moveTo(heartX + heartSize / 2f, heartY + heartSize * 0.3f)
        heartPath.cubicTo(
            heartX + heartSize * 0.2f, heartY,
            heartX, heartY + heartSize * 0.3f,
            heartX + heartSize / 2f, heartY + heartSize
        )
        heartPath.cubicTo(
            heartX + heartSize, heartY + heartSize * 0.3f,
            heartX + heartSize * 0.8f, heartY,
            heartX + heartSize / 2f, heartY + heartSize * 0.3f
        )
        canvas.drawPath(heartPath, heartPaint)
        
        // Layer 5: Percentage text inside heart (if enabled)
        if (config.customConfig.showPercentage) {
            val percentageText = "${status.percentage}%"
            val textBounds = Rect()
            textPaint.getTextBounds(percentageText, 0, percentageText.length, textBounds)
            
            val textX = heartX + heartSize / 2f - textBounds.width() / 2f
            val textY = heartY + heartSize / 2f + textBounds.height() / 2f - textBounds.bottom
            
            // Use white text for better contrast on heart
            val whiteTextPaint = Paint(textPaint).apply {
                color = Color.WHITE
            }
            canvas.drawText(percentageText, textX, textY, whiteTextPaint)
        }
        
        BatteryLogger.v(EMOJI_DRAW_TAG, "Drew composite battery indicator at ($x, $centerY) with ${status.percentage}%")
        return defaultBatteryWidth + 2f + elementSpacing  // Include terminal width
    }

    // Measurement helper methods
    private fun measureTimeWidth(): Float {
        val timePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
            textSize = (12 * resources.displayMetrics.scaledDensity)  // Updated size
            typeface = Typeface.DEFAULT_BOLD
        }
        return timePaint.measureText("18:09")  // Sample time format
    }

    private fun measureSilentIconWidth(): Float {
        return (16 * resources.displayMetrics.density)  // Updated size for better visibility
    }

    private fun measureCellularIconWidth(): Float {
        return (16 * resources.displayMetrics.density)  // Updated size for better visibility
    }

    private fun measureWifiIconWidth(): Float {
        return (16 * resources.displayMetrics.density)  // Updated size for better visibility
    }

    private fun measurePercentageWidth(status: CoreBatteryStatus): Float {
        val percentageText = "${status.percentage}%"
        return textPaint.measureText(percentageText)
    }

    /**
     * Handles touch events for gesture detection
     */
    override fun onTouchEvent(event: MotionEvent): Boolean {
        BatteryLogger.v(EMOJI_GESTURE_TAG, "Touch event: ${event.action}")
        
        // Use gesture detector for swipe gestures
        val gestureHandled = gestureDetector.onTouchEvent(event)
        
        if (gestureHandled) {
            BatteryLogger.d(EMOJI_GESTURE_TAG, "Gesture handled by detector")
            return true
        }
        
        // Allow touch events to pass through for other interactions
        return super.onTouchEvent(event)
    }
    
    /**
     * Cleans up emoji view resources
     */
    fun cleanup() {
        BatteryLogger.d(TAG, "Cleaning up EmojiBatteryView resources")
        BatteryLogger.d(EMOJI_VIEW_TAG, "EMOJI_VIEW_CLEANUP_STARTED")

        emojiBitmap?.recycle()
        batteryBitmap?.recycle()
        emojiBitmap = null
        batteryBitmap = null

        BatteryLogger.d(EMOJI_VIEW_TAG, "EMOJI_VIEW_CLEANUP_COMPLETED")
    }
}
